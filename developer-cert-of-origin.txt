As of 1.18, the ANTLR plugin uses the Linux Foundation's Developer
Certificate of Origin, DCO, version 1.1. See either
https://developercertificate.org/ or the text below.

Each commit requires a "signature", which is simple as
using `-s` (not `-S`) to the git commit command: 

git commit -s -m 'This is my commit message'

Github's pull request process enforces the sig and gives
instructions on how to fix any commits that lack the sig.
See https://github.com/apps/dco for more info.

No signature is required in this file (unlike the
previous contributor's certificate of origin.)

----- https://developercertificate.org/ ------

Developer Certificate of Origin
Version 1.1

Copyright (C) 2004, 2006 The Linux Foundation and its contributors.

Everyone is permitted to copy and distribute verbatim copies of this
license document, but changing it is not allowed.


Developer's Certificate of Origin 1.1

By making a contribution to this project, I certify that:

(a) The contribution was created in whole or in part by me and I
    have the right to submit it under the open source license
    indicated in the file; or

(b) The contribution is based upon previous work that, to the best
    of my knowledge, is covered under an appropriate open source
    license and I have the right under that license to submit that
    work with modifications, whether created in whole or in part
    by me, under the same open source license (unless I am
    permitted to submit under a different license), as indicated
    in the file; or

(c) The contribution was provided directly to me by some other
    person who certified (a), (b) or (c) and I have not modified
    it.

(d) I understand and agree that this project and the contribution
    are public and that a record of the contribution (including all
    personal information I submit with it, including my sign-off) is
    maintained indefinitely and may be redistributed consistent with
    this project or the open source license(s) involved.
