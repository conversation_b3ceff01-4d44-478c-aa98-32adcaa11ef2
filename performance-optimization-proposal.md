# ANTLR插件性能优化方案

## 问题概述
在批量替换g4文件时，插件会对每个文件单独进行解析，导致编译过程卡顿。

## 具体优化建议

### 1. 实现防抖动机制
```java
// 在ANTLRv4PluginController中添加
private final Map<String, Timer> fileUpdateTimers = new HashMap<>();
private static final int DEBOUNCE_DELAY = 500; // 500ms延迟

private void scheduleGrammarUpdate(VirtualFile grammarFile) {
    String filePath = grammarFile.getPath();
    
    // 取消之前的定时器
    Timer existingTimer = fileUpdateTimers.get(filePath);
    if (existingTimer != null) {
        existingTimer.cancel();
    }
    
    // 创建新的定时器
    Timer timer = new Timer(DEBOUNCE_DELAY, e -> {
        updateGrammarObjectsFromFile(grammarFile, true);
        fileUpdateTimers.remove(filePath);
    });
    timer.setRepeats(false);
    
    fileUpdateTimers.put(filePath, timer);
    timer.start();
}
```

### 2. 批量文件检测
```java
// 检测批量操作的机制
private final AtomicInteger pendingUpdates = new AtomicInteger(0);
private volatile long lastBatchTime = 0;
private static final int BATCH_THRESHOLD = 3; // 3个文件以上认为是批量操作
private static final long BATCH_WINDOW = 1000; // 1秒内的操作认为是批量

private boolean isBatchOperation() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastBatchTime < BATCH_WINDOW) {
        return pendingUpdates.get() >= BATCH_THRESHOLD;
    }
    return false;
}
```

### 3. 异步处理优化
```java
// 改进后的grammarFileSavedEvent方法
public void grammarFileSavedEvent(VirtualFile grammarFile) {
    Long modCount = grammarFile.getModificationCount();
    String grammarFilePath = grammarFile.getPath();

    if (grammarFileMods.containsKey(grammarFilePath) && 
        grammarFileMods.get(grammarFilePath).equals(modCount)) {
        return;
    }

    grammarFileMods.put(grammarFilePath, modCount);
    pendingUpdates.incrementAndGet();
    lastBatchTime = System.currentTimeMillis();

    if (isBatchOperation()) {
        // 批量操作：延迟处理
        scheduleGrammarUpdate(grammarFile);
    } else {
        // 单文件操作：正常处理
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            updateGrammarObjectsFromFile(grammarFile, true);
            pendingUpdates.decrementAndGet();
            
            SwingUtilities.invokeLater(() -> {
                if (previewPanel != null) {
                    previewPanel.grammarFileSaved(grammarFile);
                }
            });
        });
    }
}
```

### 4. 缓存改进
```java
// 添加更智能的缓存机制
private final Map<String, GrammarCacheEntry> grammarCache = new ConcurrentHashMap<>();

static class GrammarCacheEntry {
    final Grammar grammar;
    final LexerGrammar lexerGrammar;
    final long lastModified;
    final long fileSize;
    
    GrammarCacheEntry(Grammar g, LexerGrammar lg, long modified, long size) {
        this.grammar = g;
        this.lexerGrammar = lg;
        this.lastModified = modified;
        this.fileSize = size;
    }
    
    boolean isValid(VirtualFile file) {
        return lastModified == file.getModificationCount() && 
               fileSize == file.getLength();
    }
}
```

### 5. 配置选项
```java
// 在ANTLRv4ProjectSettings中添加性能配置
public class ANTLRv4PerformanceSettings {
    private boolean enableBatchProcessing = true;
    private int debounceDelay = 500;
    private int batchThreshold = 3;
    private boolean disablePreviewDuringBatch = true;
    
    // getter/setter方法...
}
```

## 实现步骤

### 第一阶段：基础优化
1. 实现防抖动机制
2. 将文件解析移到后台线程
3. 添加批量操作检测

### 第二阶段：高级优化
1. 改进缓存机制
2. 添加用户配置选项
3. 优化预览窗口更新策略

### 第三阶段：完善优化
1. 添加性能监控
2. 优化内存使用
3. 添加进度指示器

## 关键代码修改点

### ANTLRv4PluginController.java
- `grammarFileSavedEvent()` - 添加防抖动和批量检测
- `updateGrammarObjectsFromFile()` - 异步化处理
- 添加配置管理和缓存优化

### ParsingUtils.java  
- `loadGrammars()` - 添加缓存检查
- 优化Grammar对象创建过程

### 新增配置文件
- 添加性能相关的用户设置
- 支持禁用实时预览等选项