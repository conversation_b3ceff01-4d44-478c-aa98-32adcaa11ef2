buildscript {
    repositories {
        mavenCentral()
    }
}

plugins {
    id "org.jetbrains.intellij" version "1.17.4"
}

wrapper {
    gradleVersion = '7.5'
}

group 'antlr'
version pluginVersion

apply plugin: 'java'
apply plugin: 'org.jetbrains.intellij'
apply plugin: 'antlr'

compileJava {
    sourceCompatibility = '17'
    targetCompatibility = '17'
}

intellij {
    version = ideaVersion

    pluginName = 'antlr-intellij-plugin-v4'
    downloadSources = true
    updateSinceUntilBuild = true

    patchPluginXml {
        sinceBuild = sinceBuildVersion
        untilBuild = untilBuildVersion
    }
}

repositories {
    mavenCentral()
    mavenLocal()
    maven { // Gets snapshots of antlr if needed
        url "https://oss.sonatype.org/content/repositories/snapshots/"
    }
}

dependencies {
    antlr("org.antlr:antlr4:$antlr4Version") { // use ANTLR version 4
        exclude group:'com.ibm.icu', module:'icu4j'
    }
    implementation "org.antlr:antlr4-intellij-adaptor:0.1"
    implementation group: 'org.jfree', name: 'org.jfree.svg', version: '5.0.6'
    testImplementation group: 'junit', name: 'junit', version: '4.13.2'
    testImplementation group: 'org.mockito', name: 'mockito-core', version: '5.15.2'
}

generateGrammarSource {
    include("**/ANTLRv4*.g4")

    arguments += [
            "-package", "org.antlr.intellij.plugin.parser",
            "-lib", "src/main/antlr/org/antlr/intellij/plugin/parser",
            "-Xexact-output-dir"
    ]
}

test {
    testLogging {
        events "failed"
        exceptionFormat "full"
    }
}

runIde {
    jvmArgs '-Xmx8G'
}
