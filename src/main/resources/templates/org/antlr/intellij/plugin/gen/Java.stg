tokenTypeFile(package, grammarName, tokenTypeClassName, tokenNames, keywords, ruleNames, commentTokens, whitespaceTokens) ::= <<
package <package>;

import com.intellij.psi.TokenType;
import com.intellij.psi.tree.IElementType;
import com.intellij.psi.tree.TokenSet;
import org.antlr.v4.runtime.Token;

import org.antlr.intellij.plugin.ANTLRv4TokenType;

import static <package>.<grammarName>Lexer.*;

public class <grammarName>TokenTypes {
    public static IElementType BAD_CHARACTER = TokenType.BAD_CHARACTER;
	public static <tokenTypeClassName> BAD_TOKEN = new <tokenTypeClassName>("BAD_TOKEN");
    public static <tokenTypeClassName> EOF = new <tokenTypeClassName>(Token.EOF, "EOF");

    <tokenNames:{t | public static <tokenTypeClassName> <t> = new <tokenTypeClassName>(ANTLRv4Lexer.<t>, "<t>");}; separator="\n">

    <ruleNames:{r | public static <tokenTypeClassName> <r> = new <tokenTypeClassName>("<r>");}; separator="\n">

    public static TokenSet COMMENTS = TokenSet.create(<commentTokens; separator=",">);
    public static TokenSet WHITESPACES = TokenSet.create(BAD_TOKEN, <whitespaceTokens; separator=",">);
	public static TokenSet KEYWORDS = TokenSet.create(<keywords; wrap, anchor, separator=",">);

    public static <tokenTypeClassName>[] typeToIDEATokenType = new <tokenTypeClassName>[<length(tokenNames)>+1];
    public static <tokenTypeClassName>[] ruleToIDEATokenType = new <tokenTypeClassName>[<length(ruleNames)>+1];

    static {
    	<tokenNames:{t | typeToIDEATokenType[<grammarName>Lexer.<t>] = <t>;}; separator="\n">
    	<ruleNames:{r | ruleToIDEATokenType[<i0>] = <r>;}; separator="\n">
    }
}
>>
