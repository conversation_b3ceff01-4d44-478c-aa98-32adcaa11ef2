<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ActiveLayerIndex</key>
	<integer>0</integer>
	<key>ApplicationVersion</key>
	<array>
		<string>com.omnigroup.OmniGraffle</string>
		<string>139.18.0.187838</string>
	</array>
	<key>AutoAdjust</key>
	<true/>
	<key>BackgroundGraphic</key>
	<dict>
		<key>Bounds</key>
		<string>{{0, 0}, {576, 733}}</string>
		<key>Class</key>
		<string>SolidGraphic</string>
		<key>FontInfo</key>
		<dict>
			<key>Font</key>
			<string>LucidaBright</string>
			<key>Size</key>
			<real>72</real>
		</dict>
		<key>ID</key>
		<integer>2</integer>
		<key>Style</key>
		<dict>
			<key>shadow</key>
			<dict>
				<key>Draws</key>
				<string>NO</string>
			</dict>
			<key>stroke</key>
			<dict>
				<key>Draws</key>
				<string>NO</string>
			</dict>
		</dict>
	</dict>
	<key>BaseZoom</key>
	<integer>0</integer>
	<key>CanvasOrigin</key>
	<string>{0, 0}</string>
	<key>ColumnAlign</key>
	<integer>1</integer>
	<key>ColumnSpacing</key>
	<real>36</real>
	<key>CreationDate</key>
	<string>2013-11-30 20:59:50 +0000</string>
	<key>Creator</key>
	<string>Terence Parr</string>
	<key>DisplayScale</key>
	<string>1 0/72 in = 1 0/72 in</string>
	<key>FileType</key>
	<string>flat</string>
	<key>GraphDocumentVersion</key>
	<integer>8</integer>
	<key>GraphicsList</key>
	<array>
		<dict>
			<key>Bounds</key>
			<string>{{124.89488864205123, 197.87982116882378}, {38, 81}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FitText</key>
			<string>YES</string>
			<key>Flow</key>
			<string>Resize</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>b</key>
					<string>1</string>
					<key>g</key>
					<string>1</string>
					<key>r</key>
					<string>1</string>
				</dict>
				<key>Font</key>
				<string>LucidaBright-Italic</string>
				<key>Size</key>
				<real>68</real>
			</dict>
			<key>ID</key>
			<integer>36901</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
				<key>stroke</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Pad</key>
				<integer>0</integer>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1138\cocoasubrtf510
{\fonttbl\f0\fnil\fcharset0 LucidaBright;}
{\colortbl;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\qc

\f0\fs136 \cf1 L}</string>
				<key>VerticalPad</key>
				<integer>0</integer>
			</dict>
			<key>Wrap</key>
			<string>NO</string>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{95.94756083641272, 198.43244442777504}, {89.894645690917969, 89.894649463890133}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FontInfo</key>
			<dict>
				<key>Font</key>
				<string>LucidaBright</string>
				<key>Size</key>
				<real>68</real>
			</dict>
			<key>ID</key>
			<integer>36904</integer>
			<key>Rotation</key>
			<real>332.50872802734375</real>
			<key>Shape</key>
			<string>Circle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0.6</string>
						<key>g</key>
						<string>1</string>
						<key>r</key>
						<string>0.8</string>
					</dict>
					<key>FillType</key>
					<integer>2</integer>
					<key>GradientAngle</key>
					<real>90</real>
					<key>GradientColor</key>
					<dict>
						<key>b</key>
						<string>0.25098</string>
						<key>g</key>
						<string>0.501961</string>
						<key>r</key>
						<string>0</string>
					</dict>
					<key>MiddleColor</key>
					<dict>
						<key>b</key>
						<string>0.143648</string>
						<key>g</key>
						<string>0.776547</string>
						<key>r</key>
						<string>0.179675</string>
					</dict>
					<key>MiddleFraction</key>
					<real>0.30952376127243042</real>
					<key>TrippleBlend</key>
					<string>YES</string>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0.866667</string>
						<key>g</key>
						<string>0.866667</string>
						<key>r</key>
						<string>0.866667</string>
					</dict>
					<key>Fuzziness</key>
					<real>1.2369766235351562</real>
					<key>ShadowVector</key>
					<string>{1, 1}</string>
				</dict>
				<key>stroke</key>
				<dict>
					<key>Color</key>
					<dict>
						<key>b</key>
						<string>0.8</string>
						<key>g</key>
						<string>0.8</string>
						<key>r</key>
						<string>0.8</string>
					</dict>
				</dict>
			</dict>
		</dict>
		<dict>
			<key>Class</key>
			<string>Group</string>
			<key>Graphics</key>
			<array>
				<dict>
					<key>Bounds</key>
					<string>{{412.57086493277995, 382.85923964517917}, {86.982810942412556, 52.341026670099602}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Font</key>
						<string>Helvetica-Bold</string>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>36899</integer>
					<key>Shape</key>
					<string>Bezier</string>
					<key>ShapeData</key>
					<dict>
						<key>UnitPoints</key>
						<array>
							<string>{-0.49098399999999998, 0.5}</string>
							<string>{-0.49098399999999998, 0.5}</string>
							<string>{-0.099021899999999996, 0.15590699999999999}</string>
							<string>{0.18135999999999999, 0.17175299999999999}</string>
							<string>{0.461754, 0.18765299999999999}</string>
							<string>{0.50000599999999995, 0.12402299999999999}</string>
							<string>{0.5, 0.12413}</string>
							<string>{0.5, 0.12413}</string>
							<string>{0.35023300000000002, -0.75490199999999996}</string>
							<string>{-0.19781099999999999, -0.42655599999999999}</string>
							<string>{-0.58013499999999996, -0.098243700000000003}</string>
							<string>{-0.49098399999999998, 0.5}</string>
						</array>
					</dict>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>a</key>
								<string>0.55</string>
								<key>b</key>
								<string>1</string>
								<key>g</key>
								<string>1</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>FillType</key>
							<integer>2</integer>
							<key>GradientAngle</key>
							<real>57</real>
							<key>GradientColor</key>
							<dict>
								<key>a</key>
								<string>0.07</string>
								<key>w</key>
								<string>1</string>
							</dict>
							<key>MiddleColor</key>
							<dict>
								<key>a</key>
								<string>0.15</string>
								<key>w</key>
								<string>1</string>
							</dict>
							<key>MiddleFraction</key>
							<real>0.29365080595016479</real>
							<key>TrippleBlend</key>
							<string>YES</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.8</string>
								<key>g</key>
								<string>0.8</string>
								<key>r</key>
								<string>0.8</string>
							</dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>5.1873583793640137</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
					</dict>
					<key>Wrap</key>
					<string>NO</string>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{411.67289076685637, 382.17289185070354}, {89.894649463890019, 89.894645690917969}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>36900</integer>
					<key>Rotation</key>
					<real>332.50872802734375</real>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.903075</string>
								<key>g</key>
								<string>0.634122</string>
								<key>r</key>
								<string>0.392525</string>
							</dict>
							<key>FillType</key>
							<integer>2</integer>
							<key>GradientAngle</key>
							<real>90</real>
							<key>GradientColor</key>
							<dict>
								<key>b</key>
								<string>0.903075</string>
								<key>g</key>
								<string>0.236072</string>
								<key>r</key>
								<string>0.154284</string>
							</dict>
							<key>MiddleColor</key>
							<dict>
								<key>b</key>
								<string>0.871287</string>
								<key>g</key>
								<string>0.611801</string>
								<key>r</key>
								<string>0.378709</string>
							</dict>
							<key>MiddleFraction</key>
							<real>0.30952376127243042</real>
							<key>TrippleBlend</key>
							<string>YES</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.866667</string>
								<key>g</key>
								<string>0.866667</string>
								<key>r</key>
								<string>0.866667</string>
							</dict>
							<key>Fuzziness</key>
							<real>1.2369766235351562</real>
							<key>ShadowVector</key>
							<string>{1, 1}</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.8</string>
								<key>g</key>
								<string>0.8</string>
								<key>r</key>
								<string>0.8</string>
							</dict>
						</dict>
					</dict>
				</dict>
			</array>
			<key>ID</key>
			<integer>36898</integer>
		</dict>
		<dict>
			<key>Bounds</key>
			<string>{{277.43377685546875, 200.37977600097656}, {42, 86}}</string>
			<key>Class</key>
			<string>ShapedGraphic</string>
			<key>FitText</key>
			<string>YES</string>
			<key>Flow</key>
			<string>Resize</string>
			<key>FontInfo</key>
			<dict>
				<key>Color</key>
				<dict>
					<key>b</key>
					<string>1</string>
					<key>g</key>
					<string>1</string>
					<key>r</key>
					<string>1</string>
				</dict>
				<key>Font</key>
				<string>LucidaBright</string>
				<key>Size</key>
				<real>72</real>
			</dict>
			<key>ID</key>
			<integer>36897</integer>
			<key>Shape</key>
			<string>Rectangle</string>
			<key>Style</key>
			<dict>
				<key>fill</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
				<key>shadow</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
				<key>stroke</key>
				<dict>
					<key>Draws</key>
					<string>NO</string>
				</dict>
			</dict>
			<key>Text</key>
			<dict>
				<key>Pad</key>
				<integer>0</integer>
				<key>Text</key>
				<string>{\rtf1\ansi\ansicpg1252\cocoartf1138\cocoasubrtf510
{\fonttbl\f0\fnil\fcharset0 LucidaBright;}
{\colortbl;\red255\green255\blue255;\red255\green255\blue255;}
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\qc

\f0\fs144 \cf2 P}</string>
				<key>VerticalPad</key>
				<integer>0</integer>
			</dict>
			<key>Wrap</key>
			<string>NO</string>
		</dict>
		<dict>
			<key>Class</key>
			<string>Group</string>
			<key>Graphics</key>
			<array>
				<dict>
					<key>Bounds</key>
					<string>{{250.3844319994036, 199.11880751444144}, {86.982810942412556, 52.341026670099602}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>FontInfo</key>
					<dict>
						<key>Font</key>
						<string>Helvetica-Bold</string>
						<key>Size</key>
						<real>12</real>
					</dict>
					<key>ID</key>
					<integer>36895</integer>
					<key>Shape</key>
					<string>Bezier</string>
					<key>ShapeData</key>
					<dict>
						<key>UnitPoints</key>
						<array>
							<string>{-0.49098399999999998, 0.5}</string>
							<string>{-0.49098399999999998, 0.5}</string>
							<string>{-0.099021899999999996, 0.15590699999999999}</string>
							<string>{0.18135999999999999, 0.17175299999999999}</string>
							<string>{0.461754, 0.18765299999999999}</string>
							<string>{0.50000599999999995, 0.12402299999999999}</string>
							<string>{0.5, 0.12413}</string>
							<string>{0.5, 0.12413}</string>
							<string>{0.35023300000000002, -0.75490199999999996}</string>
							<string>{-0.19781099999999999, -0.42655599999999999}</string>
							<string>{-0.58013499999999996, -0.098243700000000003}</string>
							<string>{-0.49098399999999998, 0.5}</string>
						</array>
					</dict>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>a</key>
								<string>0.55</string>
								<key>b</key>
								<string>1</string>
								<key>g</key>
								<string>1</string>
								<key>r</key>
								<string>1</string>
							</dict>
							<key>FillType</key>
							<integer>2</integer>
							<key>GradientAngle</key>
							<real>57</real>
							<key>GradientColor</key>
							<dict>
								<key>a</key>
								<string>0.07</string>
								<key>w</key>
								<string>1</string>
							</dict>
							<key>MiddleColor</key>
							<dict>
								<key>a</key>
								<string>0.15</string>
								<key>w</key>
								<string>1</string>
							</dict>
							<key>MiddleFraction</key>
							<real>0.29365080595016479</real>
							<key>TrippleBlend</key>
							<string>YES</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.8</string>
								<key>g</key>
								<string>0.8</string>
								<key>r</key>
								<string>0.8</string>
							</dict>
							<key>Draws</key>
							<string>NO</string>
							<key>Fuzziness</key>
							<real>5.1873583793640137</real>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Draws</key>
							<string>NO</string>
						</dict>
					</dict>
					<key>Wrap</key>
					<string>NO</string>
				</dict>
				<dict>
					<key>Bounds</key>
					<string>{{249.48645783348002, 198.43245971996578}, {89.894649463890019, 89.894645690917969}}</string>
					<key>Class</key>
					<string>ShapedGraphic</string>
					<key>ID</key>
					<integer>36896</integer>
					<key>Rotation</key>
					<real>332.50872802734375</real>
					<key>Shape</key>
					<string>Circle</string>
					<key>Style</key>
					<dict>
						<key>fill</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.903075</string>
								<key>g</key>
								<string>0.634122</string>
								<key>r</key>
								<string>0.392525</string>
							</dict>
							<key>FillType</key>
							<integer>2</integer>
							<key>GradientAngle</key>
							<real>90</real>
							<key>GradientColor</key>
							<dict>
								<key>b</key>
								<string>0.903075</string>
								<key>g</key>
								<string>0.236072</string>
								<key>r</key>
								<string>0.154284</string>
							</dict>
							<key>MiddleColor</key>
							<dict>
								<key>b</key>
								<string>0.871287</string>
								<key>g</key>
								<string>0.611801</string>
								<key>r</key>
								<string>0.378709</string>
							</dict>
							<key>MiddleFraction</key>
							<real>0.30952376127243042</real>
							<key>TrippleBlend</key>
							<string>YES</string>
						</dict>
						<key>shadow</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.866667</string>
								<key>g</key>
								<string>0.866667</string>
								<key>r</key>
								<string>0.866667</string>
							</dict>
							<key>Fuzziness</key>
							<real>1.2369766235351562</real>
							<key>ShadowVector</key>
							<string>{1, 1}</string>
						</dict>
						<key>stroke</key>
						<dict>
							<key>Color</key>
							<dict>
								<key>b</key>
								<string>0.8</string>
								<key>g</key>
								<string>0.8</string>
								<key>r</key>
								<string>0.8</string>
							</dict>
						</dict>
					</dict>
				</dict>
			</array>
			<key>ID</key>
			<integer>36894</integer>
		</dict>
	</array>
	<key>GridInfo</key>
	<dict/>
	<key>GuidesLocked</key>
	<string>NO</string>
	<key>GuidesVisible</key>
	<string>YES</string>
	<key>HPages</key>
	<integer>1</integer>
	<key>ImageCounter</key>
	<integer>2</integer>
	<key>KeepToScale</key>
	<false/>
	<key>Layers</key>
	<array>
		<dict>
			<key>Lock</key>
			<string>NO</string>
			<key>Name</key>
			<string>Layer 1</string>
			<key>Print</key>
			<string>YES</string>
			<key>View</key>
			<string>YES</string>
		</dict>
	</array>
	<key>LayoutInfo</key>
	<dict>
		<key>Animate</key>
		<string>NO</string>
		<key>circoMinDist</key>
		<real>18</real>
		<key>circoSeparation</key>
		<real>0.0</real>
		<key>layoutEngine</key>
		<string>dot</string>
		<key>neatoSeparation</key>
		<real>0.0</real>
		<key>twopiSeparation</key>
		<real>0.0</real>
	</dict>
	<key>LinksVisible</key>
	<string>NO</string>
	<key>MagnetsVisible</key>
	<string>NO</string>
	<key>MasterSheets</key>
	<array/>
	<key>ModificationDate</key>
	<string>2013-11-30 22:22:59 +0000</string>
	<key>Modifier</key>
	<string>Terence Parr</string>
	<key>NotesVisible</key>
	<string>NO</string>
	<key>Orientation</key>
	<integer>2</integer>
	<key>OriginVisible</key>
	<string>NO</string>
	<key>PageBreaks</key>
	<string>YES</string>
	<key>PrintInfo</key>
	<dict>
		<key>NSBottomMargin</key>
		<array>
			<string>float</string>
			<string>41</string>
		</array>
		<key>NSHorizonalPagination</key>
		<array>
			<string>coded</string>
			<string>BAtzdHJlYW10eXBlZIHoA4QBQISEhAhOU051bWJlcgCEhAdOU1ZhbHVlAISECE5TT2JqZWN0AIWEASqEhAFxlwCG</string>
		</array>
		<key>NSLeftMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSPaperSize</key>
		<array>
			<string>size</string>
			<string>{612, 792}</string>
		</array>
		<key>NSPrintReverseOrientation</key>
		<array>
			<string>int</string>
			<string>0</string>
		</array>
		<key>NSRightMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
		<key>NSTopMargin</key>
		<array>
			<string>float</string>
			<string>18</string>
		</array>
	</dict>
	<key>PrintOnePage</key>
	<false/>
	<key>ReadOnly</key>
	<string>NO</string>
	<key>RowAlign</key>
	<integer>1</integer>
	<key>RowSpacing</key>
	<real>36</real>
	<key>SheetTitle</key>
	<string>Canvas 1</string>
	<key>SmartAlignmentGuidesActive</key>
	<string>YES</string>
	<key>SmartDistanceGuidesActive</key>
	<string>YES</string>
	<key>UniqueID</key>
	<integer>1</integer>
	<key>UseEntirePage</key>
	<false/>
	<key>VPages</key>
	<integer>1</integer>
	<key>WindowInfo</key>
	<dict>
		<key>CurrentSheet</key>
		<integer>0</integer>
		<key>ExpandedCanvases</key>
		<array>
			<dict>
				<key>name</key>
				<string>Canvas 1</string>
			</dict>
		</array>
		<key>Frame</key>
		<string>{{588, 95}, {710, 872}}</string>
		<key>ListView</key>
		<true/>
		<key>OutlineWidth</key>
		<integer>142</integer>
		<key>RightSidebar</key>
		<false/>
		<key>ShowRuler</key>
		<true/>
		<key>Sidebar</key>
		<true/>
		<key>SidebarWidth</key>
		<integer>120</integer>
		<key>VisibleRegion</key>
		<string>{{0, 0}, {575, 733}}</string>
		<key>Zoom</key>
		<real>1</real>
		<key>ZoomValues</key>
		<array>
			<array>
				<string>Canvas 1</string>
				<real>1</real>
				<real>1</real>
			</array>
		</array>
	</dict>
</dict>
</plist>
