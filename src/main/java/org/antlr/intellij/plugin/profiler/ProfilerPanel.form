<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="org.antlr.intellij.plugin.profiler.ProfilerPanel">
  <grid id="27dc6" binding="outerPanel" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="20" y="20" width="500" height="400"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="7888c" binding="statsPanel" layout-manager="GridLayoutManager" row-count="12" column-count="3" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="5" bottom="0" right="0"/>
        <constraints border-constraint="East"/>
        <properties/>
        <border type="none"/>
        <children>
          <component id="1284d" class="javax.swing.JLabel">
            <constraints>
              <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false">
                <preferred-size width="130" height="16"/>
              </grid>
            </constraints>
            <properties>
              <text value="Parse time (ms):"/>
            </properties>
          </component>
          <component id="c8087" class="javax.swing.JLabel">
            <constraints>
              <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false">
                <preferred-size width="130" height="16"/>
              </grid>
            </constraints>
            <properties>
              <text value="Prediction time (ms):"/>
            </properties>
          </component>
          <component id="47bd6" class="javax.swing.JLabel">
            <constraints>
              <grid row="4" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false">
                <preferred-size width="130" height="16"/>
              </grid>
            </constraints>
            <properties>
              <text value="Lookahead burden:"/>
            </properties>
          </component>
          <component id="a2685" class="javax.swing.JLabel">
            <constraints>
              <grid row="5" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false">
                <preferred-size width="130" height="16"/>
              </grid>
            </constraints>
            <properties>
              <text value="DFA cache miss rate:"/>
            </properties>
          </component>
          <vspacer id="7b0fe">
            <constraints>
              <grid row="11" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false">
                <preferred-size width="-1" height="14"/>
              </grid>
            </constraints>
          </vspacer>
          <hspacer id="bff4c">
            <constraints>
              <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
          </hspacer>
          <component id="5e524" class="javax.swing.JLabel" binding="parseTimeField">
            <constraints>
              <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <component id="a8321" class="javax.swing.JLabel" binding="predictionTimeField">
            <constraints>
              <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <component id="8dee7" class="javax.swing.JLabel" binding="lookaheadBurdenField">
            <constraints>
              <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <component id="c3bee" class="javax.swing.JLabel" binding="cacheMissRateField">
            <constraints>
              <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <component id="ddd98" class="javax.swing.JLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false">
                <preferred-size width="130" height="16"/>
              </grid>
            </constraints>
            <properties>
              <text value="Input size:"/>
            </properties>
          </component>
          <component id="512ef" class="javax.swing.JLabel" binding="inputSizeField">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <component id="d973" class="javax.swing.JLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="Number of tokens:"/>
            </properties>
          </component>
          <component id="724de" class="javax.swing.JLabel" binding="numTokensField">
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="0"/>
            </properties>
          </component>
          <grid id="65072" layout-manager="GridLayoutManager" row-count="4" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="7" column="0" row-span="4" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="etched"/>
            <children>
              <component id="c0a46" class="javax.swing.JLabel" binding="ambiguityColorLabel" custom-create="true">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Ambiguity"/>
                </properties>
              </component>
              <component id="7b387" class="javax.swing.JLabel" binding="contextSensitivityColorLabel" custom-create="true">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Context-sensitivity"/>
                </properties>
              </component>
              <component id="d40cd" class="javax.swing.JLabel" binding="predEvaluationColorLabel" custom-create="true">
                <constraints>
                  <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Predicate evaluation"/>
                </properties>
              </component>
              <component id="b089f" class="javax.swing.JLabel" binding="deepestLookaheadLabel" custom-create="true">
                <constraints>
                  <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Deepest lookahead"/>
                </properties>
              </component>
            </children>
          </grid>
          <component id="a3fa2" class="javax.swing.JCheckBox" binding="expertCheckBox" custom-create="true">
            <constraints>
              <grid row="6" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <text value="Show expert columns"/>
            </properties>
          </component>
        </children>
      </grid>
      <scrollpane id="7251b">
        <constraints border-constraint="Center"/>
        <properties/>
        <border type="none"/>
        <children>
          <component id="c9e84" class="com.intellij.ui.table.JBTable" binding="profilerDataTable" custom-create="true">
            <constraints/>
            <properties>
              <preferredScrollableViewportSize width="800" height="400"/>
            </properties>
          </component>
        </children>
      </scrollpane>
    </children>
  </grid>
</form>
