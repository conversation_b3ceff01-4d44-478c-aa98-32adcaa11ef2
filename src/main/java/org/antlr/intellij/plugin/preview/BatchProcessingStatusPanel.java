package org.antlr.intellij.plugin.preview;

import com.intellij.openapi.project.Project;
import com.intellij.ui.JBColor;
import com.intellij.util.ui.JBUI;
import org.antlr.intellij.plugin.ANTLRv4PluginController;

import javax.swing.*;
import java.awt.*;

/**
 * A simple status panel that shows when batch processing is active
 */
public class BatchProcessingStatusPanel extends JPanel {
    private final JLabel statusLabel;
    private final Project project;
    private Timer updateTimer;
    
    public BatchProcessingStatusPanel(Project project) {
        this.project = project;
        setLayout(new BorderLayout());
        setBorder(JBUI.Borders.empty(2, 5));
        setBackground(JBColor.YELLOW.darker());
        
        statusLabel = new JLabel("Batch processing grammar files...");
        statusLabel.setForeground(JBColor.BLACK);
        statusLabel.setFont(statusLabel.getFont().deriveFont(Font.BOLD));
        
        add(statusLabel, BorderLayout.CENTER);
        
        // Initially hidden
        setVisible(false);
        
        // Start monitoring timer
        startMonitoring();
    }
    
    private void startMonitoring() {
        updateTimer = new Timer(200, e -> updateStatus());
        updateTimer.start();
    }
    
    private void updateStatus() {
        if (project.isDisposed()) {
            dispose();
            return;
        }

        ANTLRv4PluginController controller = ANTLRv4PluginController.getInstance(project);
        if (controller != null) {
            boolean isBatchActive = controller.isBatchProcessingActive();
            if (isBatchActive != isVisible()) {
                setVisible(isBatchActive);
                if (getParent() != null) {
                    getParent().revalidate();
                    getParent().repaint();
                }
            }
        }
    }
    
    public void dispose() {
        if (updateTimer != null) {
            updateTimer.stop();
            updateTimer = null;
        }
    }
}
