<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="org.antlr.intellij.plugin.configdialogs.ConfigANTLRPerGrammar">
  <grid id="27dc6" binding="dialogContents" layout-manager="GridLayoutManager" row-count="10" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="0" left="0" bottom="0" right="0"/>
    <constraints>
      <xy x="20" y="20" width="630" height="292"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <component id="ae2b3" class="javax.swing.JLabel">
        <constraints>
          <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Location of imported grammars"/>
        </properties>
      </component>
      <component id="3ead0" class="javax.swing.JLabel">
        <constraints>
          <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Grammar file encoding; e.g., euc-jp"/>
        </properties>
      </component>
      <component id="1cd51" class="javax.swing.JTextField" binding="fileEncodingField">
        <constraints>
          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
            <preferred-size width="150" height="-1"/>
          </grid>
        </constraints>
        <properties/>
      </component>
      <component id="de85f" class="javax.swing.JCheckBox" binding="generateParseTreeVisitorCheckBox" default-binding="true">
        <constraints>
          <grid row="8" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="generate parse tree visitor"/>
        </properties>
      </component>
      <component id="e1f62" class="javax.swing.JCheckBox" binding="generateParseTreeListenerCheckBox" default-binding="true">
        <constraints>
          <grid row="7" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <selected value="true"/>
          <text value="generate parse tree listener (default)"/>
        </properties>
      </component>
      <component id="1adf7" class="javax.swing.JLabel">
        <constraints>
          <grid row="4" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Package/namespace for the generated code"/>
        </properties>
      </component>
      <component id="701ae" class="javax.swing.JTextField" binding="packageField">
        <constraints>
          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
            <preferred-size width="150" height="-1"/>
          </grid>
        </constraints>
        <properties/>
      </component>
      <component id="f5283" class="javax.swing.JLabel">
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Output directory where all output is generated"/>
        </properties>
      </component>
      <component id="a65b1" class="com.intellij.openapi.ui.TextFieldWithBrowseButton" binding="outputDirField">
        <constraints>
          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="df9a9" class="com.intellij.openapi.ui.TextFieldWithBrowseButton" binding="libDirField">
        <constraints>
          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="79b55" class="javax.swing.JCheckBox" binding="autoGenerateParsersCheckBox">
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Auto-generate parsers upon save"/>
        </properties>
      </component>
      <component id="64571" class="javax.swing.JLabel">
        <constraints>
          <grid row="5" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Language (e.g., Java, Python2, CSharp, ...)"/>
        </properties>
      </component>
      <component id="6c6d2" class="javax.swing.JTextField" binding="languageField">
        <constraints>
          <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
            <preferred-size width="150" height="-1"/>
          </grid>
        </constraints>
        <properties/>
      </component>
      <vspacer id="39a11">
        <constraints>
          <grid row="9" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
        </constraints>
      </vspacer>
      <component id="beca9" class="javax.swing.JLabel">
        <constraints>
          <grid row="6" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="1" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Case transformation in the Preview window"/>
        </properties>
      </component>
      <component id="6093" class="javax.swing.JComboBox" binding="caseTransformation" custom-create="true">
        <constraints>
          <grid row="6" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
    </children>
  </grid>
</form>
