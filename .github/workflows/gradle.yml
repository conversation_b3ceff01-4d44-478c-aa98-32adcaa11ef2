# This workflow will build a Java project with Gradle
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-gradle

name: Java CI

on: [ push, pull_request ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        env:
          # see https://www.jetbrains.com/idea/download/previous.html
          # and https://www.jetbrains.com/intellij-repository/snapshots/
          - IDEA_VERSION: IC-2022.3 # Oldest supported version
          - IDEA_VERSION: IC-2023.1
          - IDEA_VERSION: IC-2023.3.2

    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: 17
          distribution: temurin
      - name: Build with Gradle
        run: |
          ./gradlew -PideaVersion=${IDEA_VERSION} check buildPlugin
        env: ${{ matrix.env }}
      - name: Archive distribution artifact
        uses: actions/upload-artifact@v3
        with:
          name: "antlr-intellij-development"
          path: build/distributions/antlr-intellij-plugin-v4-*.zip
        if: matrix.env.IDEA_VERSION == 'IC-2022.3'
