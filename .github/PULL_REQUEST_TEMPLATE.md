@@ -1,5 +1,23 @@
<!--
Thank you for proposing a contribution to the ANTLR jetbrains plugin!

As of 1.18, we use the Linux Foundation's Developer
Certificate of Origin, DCO, version 1.1. See either
https://developercertificate.org/ or file 
contributors-cert-of-origin.txt in the main directory.

Each commit requires a "signature", which is simple as
using `-s` (not `-S`) to the git commit command: 

git commit -s -m 'This is my commit message'

Github's pull request process enforces the sig and gives
instructions on how to fix any commits that lack the sig.
See https://github.com/apps/dco for more info.

No signature is required in this file (unlike the
previous contributor's certificate of origin.)
-->

