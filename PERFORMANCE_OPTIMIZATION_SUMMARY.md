# ANTLR v4插件性能优化总结

## 问题描述

在项目编译过程中批量替换大量.g4文件（约几十个文件）时，ANTLR v4插件会触发大量的文件变更处理，导致IDEA界面卡死或响应极慢。

## 优化方案实施

### 1. 核心批量处理优化 ✅

**问题**：原有实现为每个文件变更创建独立的Timer，在批量操作时产生大量Timer对象。

**解决方案**：
- 实现了真正的批量队列机制（`batchUpdateQueue`）
- 使用单一Timer替换多个Timer管理
- 批量处理完成后统一更新UI

**关键代码改进**：
```java
// 新增批量处理字段
private final Set<VirtualFile> batchUpdateQueue = Collections.synchronizedSet(new HashSet<>());
private Timer batchProcessingTimer = null;
private volatile boolean isBatchProcessingActive = false;

// 优化的批量处理方法
private void addToBatchQueue(VirtualFile grammarFile, ANTLRv4GrammarProperties properties)
private void processBatchQueue(ANTLRv4GrammarProperties properties)
```

### 2. 智能缓存机制优化 ✅

**问题**：原有缓存在批量操作时效果有限。

**解决方案**：
- 改进了`GrammarCacheEntry`，添加缓存时间戳
- 在批量处理期间优先使用最近的缓存条目
- 减少不必要的语法解析操作

**关键改进**：
```java
static class GrammarCacheEntry {
    final long cacheTime;  // 新增缓存时间
    
    boolean isRecentlyCreated(long maxAge) {
        return (System.currentTimeMillis() - cacheTime) < maxAge;
    }
}
```

### 3. 异步处理优化 ✅

**问题**：单文件操作仍然可能阻塞UI线程。

**解决方案**：
- 将所有文件处理操作移到后台线程
- 单文件操作也采用异步处理
- 使用`ApplicationManager.getApplication().executeOnPooledThread()`

### 4. 预览窗口更新策略优化 ✅

**问题**：批量操作时预览窗口频繁更新造成性能问题。

**解决方案**：
- 在批量处理期间跳过非当前文件的预览更新
- 只有当前活动的语法文件才会更新预览
- 添加批量处理状态检查

**关键代码**：
```java
// 在PreviewPanel.grammarFileSaved()中添加
if (controller.isBatchProcessingActive()) {
    VirtualFile currentFile = controller.getCurrentGrammarFile();
    if (currentFile == null || !currentFile.equals(grammarFile)) {
        return; // 跳过非当前文件的更新
    }
}
```

### 5. 性能配置选项扩展 ✅

**新增配置项**：
- `enableBatchProcessing`: 启用批量处理（默认true）
- `debounceDelay`: 防抖延迟时间（默认500ms）
- `batchThreshold`: 批量操作阈值（默认3个文件）
- `disablePreviewDuringBatch`: 批量时禁用预览（默认true）
- `enableSmartCaching`: 启用智能缓存（默认true）
- `maxCacheAge`: 最大缓存年龄（默认30秒）

### 6. 批量处理状态指示器 ✅

**新增功能**：
- 创建了`BatchProcessingStatusPanel`组件
- 在批量处理期间显示状态提示
- 自动监控批量处理状态并更新UI

## 性能提升效果

### 优化前
- 每个文件变更创建独立Timer（几十个Timer对象）
- 每个文件都会触发完整的语法解析
- 预览窗口频繁更新
- UI线程可能被阻塞

### 优化后
- 使用单一批量处理Timer
- 智能缓存减少重复解析
- 批量处理期间暂停不必要的预览更新
- 所有处理操作异步化
- 提供用户友好的状态指示

## 兼容性保证

- 单文件编辑时保持原有的即时响应特性
- 所有配置项都有合理的默认值
- 向后兼容现有的插件配置
- 不影响现有的功能特性

## 使用方法

1. 安装优化后的插件包：`build/distributions/antlr-intellij-plugin-v4-1.24.zip`
2. 性能设置通过项目的ANTLR配置自动生效
3. 在批量替换.g4文件时享受更流畅的体验

## 技术细节

### 关键文件修改
- `ANTLRv4PluginController.java`: 核心批量处理逻辑
- `ANTLRv4GrammarProperties.java`: 性能配置选项
- `PreviewPanel.java`: 预览更新策略优化
- `BatchProcessingStatusPanel.java`: 新增状态指示器

### 性能监控
- 添加了详细的日志记录
- 批量操作状态可视化
- 缓存命中率监控

## 结论

通过这些优化，插件在批量文件替换时的性能得到了显著提升，有效解决了IDEA界面卡顿的问题，同时保持了单文件编辑时的响应性能。
