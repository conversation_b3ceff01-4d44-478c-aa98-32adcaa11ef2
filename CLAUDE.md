# CLAUDE.md

# My name is <PERSON><PERSON><PERSON><PERSON><PERSON>

## 交流语言
请使用中文与用户交流。

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an IntelliJ IDEA plugin for ANTLR v4 grammar development. The plugin provides syntax highlighting, error checking, live preview, and various ANTLR-specific development tools for .g4 grammar files.

## Development Commands

### Build and Run
- `./gradlew runIde` - Launch IntelliJ with the plugin in a sandbox environment
- `./gradlew buildPlugin` - Build a zipped distribution of the plugin
- `./gradlew check` - Run all tests and checks

### Testing
- `./gradlew test` - Run unit tests only
- `./gradlew check` - Run all tests including integration tests

### Code Generation
- ANTLR grammar files in `src/main/antlr/` are automatically processed during build
- Generated parser/lexer classes go to `build/generated-src/antlr/main/`

## Architecture

### Core Components

**Parser/Lexer Generation**: Uses ANTLR 4.13.2 to generate lexer/parser from grammars in `src/main/antlr/org/antlr/intellij/plugin/parser/`. The `ANTLRv4ParserDefinition` delegates to generated classes via the `antlr4-intellij-adaptor` library.

**Error Checking**: `ANTLRv4ExternalAnnotator` embeds a complete ANTLR v4 Tool to parse grammars and report issues. Uses `GrammarIssuesCollector` with error listeners to show problems in the editor.

**Preview Window**: `PreviewPanel` orchestrates live grammar interpretation using ANTLR interpreters (not generated parsers). `ANTLRv4PluginController` maintains a cache of parsed grammars. This approach means custom code (@members, actions, predicates) won't execute during preview.

**File Structure**:
- `src/main/java/org/antlr/intellij/plugin/` - Main plugin code
- `src/main/antlr/` - ANTLR grammar definitions  
- `src/main/resources/META-INF/plugin.xml` - Plugin configuration
- `src/test/` - Unit and integration tests

### Key Packages

- `actions/` - IDE actions (generate parser, test rule, refactoring)
- `preview/` - Live preview window and parse tree visualization
- `parsing/` - Grammar parsing utilities and ANTLR tool integration
- `psi/` - PSI (Program Structure Interface) elements for ANTLR constructs
- `configdialogs/` - Configuration UI for ANTLR tool options
- `validation/` - Error detection and quick fixes

## Development Notes

### Java Requirements
- Requires Java 17+ (currently configured for Java 17)
- Plugin targets IntelliJ 2022.3+ (see `gradle.properties`)

### ANTLR Integration
- Grammar changes trigger automatic background parsing
- Generated code defaults to `<project-root>/gen/` unless overridden
- Keyboard shortcuts: Ctrl+Shift+G (generate parser), Ctrl+Alt+M (extract rule), Ctrl+Alt+N (inline rule)

### Testing Strategy
- Unit tests in `src/test/java/`
- Test resources in `src/test/resources/`
- Mock objects for IntelliJ platform components
- Grammar files for testing various scenarios

### Plugin Configuration
- Per-grammar configuration via right-click context menu
- Project-wide settings in IDE preferences
- Tool window for ANTLR console output and parse tree visualization